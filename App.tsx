
import React, { useState } from 'react';
import Header from './components/Header';
import ControlPanel from './components/ControlPanel';
import Canvas from './components/Canvas';
import { Design, ToolType } from './types';

const App: React.FC = () => {
  const [design, setDesign] = useState<Design | null>(null);
  const [activeTool, setActiveTool] = useState<ToolType>(ToolType.UPLOAD);
  
  const handleDesignChange = (newDesign: Design | null) => {
    if (newDesign && !design) {
      // When a new design is added, switch to the adjust tab
      setActiveTool(ToolType.ADJUST);
    } else if (!newDesign) {
      // When design is cleared, switch back to upload
      setActiveTool(ToolType.UPLOAD);
    }
    setDesign(newDesign);
  };

  const handleBookNow = () => {
    if (design) {
      alert('Your custom design has been booked for printing!');
    } else {
      alert('Please add a design to the shirt before booking.');
    }
  };

  return (
    <div className="flex flex-col h-screen font-sans bg-slate-900 text-slate-100">
      <Header onBookNow={handleBookNow} />
      <main className="flex-grow grid grid-cols-1 lg:grid-cols-3 gap-6 p-6 overflow-hidden">
        <div className="lg:col-span-1 bg-slate-800 rounded-lg shadow-2xl flex flex-col overflow-y-auto">
          <ControlPanel 
            activeTool={activeTool}
            setActiveTool={setActiveTool}
            design={design}
            onDesignChange={handleDesignChange}
          />
        </div>
        <div className="lg:col-span-2 bg-slate-800 rounded-lg shadow-2xl flex items-center justify-center p-4 overflow-hidden">
          <Canvas design={design} onDesignChange={handleDesignChange} />
        </div>
      </main>
    </div>
  );
};

export default App;
