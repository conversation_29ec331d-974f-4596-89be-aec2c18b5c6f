
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Design } from '../types';

interface CanvasProps {
  design: Design | null;
  onDesignChange: (design: Design | null) => void;
}

const Canvas: React.FC<CanvasProps> = ({ design, onDesignChange }) => {
  const [isDragging, setIsDragging] = useState(false);
  const dragStartRef = useRef<{ x: number; y: number; designX: number; designY: number } | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!design) return;
    e.preventDefault();
    setIsDragging(true);
    dragStartRef.current = {
      x: e.clientX,
      y: e.clientY,
      designX: design.position.x,
      designY: design.position.y,
    };
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !dragStartRef.current || !design || !canvasRef.current) return;
    
    const dx = e.clientX - dragStartRef.current.x;
    const dy = e.clientY - dragStartRef.current.y;
    
    const newX = dragStartRef.current.designX + dx;
    const newY = dragStartRef.current.designY + dy;
    
    onDesignChange({
        ...design,
        position: { x: newX, y: newY },
    });
  }, [isDragging, design, onDesignChange]);


  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    dragStartRef.current = null;
  }, []);

  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const designWidth = design ? (design.originalWidth / design.originalHeight) * 150 * design.size : 0;
  const designHeight = design ? 150 * design.size : 0;

  return (
    <div className="relative w-[500px] h-[600px] select-none" ref={canvasRef}>
      <img
        src="https://picsum.photos/id/1063/500/600"
        alt="T-shirt base"
        className="w-full h-full object-contain pointer-events-none rounded-lg"
      />
      <div 
        className="absolute top-[100px] left-[150px] w-[200px] h-[300px] border-2 border-dashed border-slate-500/50 pointer-events-none rounded-md"
      >
        <div className="flex items-center justify-center h-full">
            {!design && <span className="text-slate-500">Print Area</span>}
        </div>
      </div>

      {design && (
        <img
          src={design.src}
          alt={design.alt}
          onMouseDown={handleMouseDown}
          className="absolute cursor-grab active:cursor-grabbing"
          style={{
            left: `calc(150px + ${design.position.x}px)`,
            top: `calc(100px + ${design.position.y}px)`,
            width: `${designWidth}px`,
            height: `${designHeight}px`,
            transform: `rotate(${design.rotation}deg)`,
            transformOrigin: 'center center',
          }}
        />
      )}
    </div>
  );
};

export default Canvas;
