
import React from 'react';
import { Design } from '../types';
import Button from './ui/Button';
import Card from './ui/Card';

interface DesignControlsProps {
  design: Design;
  onDesignChange: (design: Design | null) => void;
}

const DesignControls: React.FC<DesignControlsProps> = ({ design, onDesignChange }) => {
  const handleSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onDesignChange({ ...design, size: parseFloat(e.target.value) });
  };

  const handleRotationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onDesignChange({ ...design, rotation: parseInt(e.target.value) });
  };
  
  const resetControls = () => {
    onDesignChange({
      ...design,
      size: 1.0,
      rotation: 0,
      position: { x: 50, y: 75 }, // Centered
    });
  }

  return (
    <Card>
      <div className="p-4 space-y-6">
        <div>
          <label htmlFor="size" className="block text-sm font-medium text-slate-300 mb-2">
            Size ({Math.round(design.size * 100)}%)
          </label>
          <input
            id="size"
            type="range"
            min="0.2"
            max="2.5"
            step="0.01"
            value={design.size}
            onChange={handleSizeChange}
            className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer accent-indigo-500"
          />
        </div>
        <div>
          <label htmlFor="rotation" className="block text-sm font-medium text-slate-300 mb-2">
            Rotation ({design.rotation}°)
          </label>
          <input
            id="rotation"
            type="range"
            min="-180"
            max="180"
            step="1"
            value={design.rotation}
            onChange={handleRotationChange}
            className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer accent-indigo-500"
          />
        </div>
        <div className="flex gap-2 pt-2 border-t border-slate-700">
            <Button onClick={resetControls} variant="secondary" className="flex-1">Reset</Button>
            <Button onClick={() => onDesignChange(null)} variant="danger" className="flex-1">
              Remove
            </Button>
        </div>
      </div>
    </Card>
  );
};

export default DesignControls;
