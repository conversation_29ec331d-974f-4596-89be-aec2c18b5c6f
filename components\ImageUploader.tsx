
import React, { useState, useCallback } from 'react';
import Card from './ui/Card';
import { UploadIcon } from './icons/UploadIcon';

interface ImageUploaderProps {
    onDesignAdd: (src: string, alt: string) => void;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ onDesignAdd }) => {
    const [error, setError] = useState<string | null>(null);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            if (!file.type.startsWith('image/')) {
                setError('Please select an image file (PNG, JPG, etc.).');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target?.result;
                if (typeof result === 'string') {
                    onDesignAdd(result, file.name);
                    setError(null);
                }
            };
            reader.onerror = () => {
                setError('Failed to read the file.');
            };
            reader.readAsDataURL(file);
        }
    };

    const onDrop = useCallback((event: React.DragEvent<HTMLLabelElement>) => {
        event.preventDefault();
        event.stopPropagation();
        const file = event.dataTransfer.files?.[0];
         if (file) {
            const fakeEvent = { target: { files: [file] } } as unknown as React.ChangeEvent<HTMLInputElement>;
            handleFileChange(fakeEvent);
        }
    }, [onDesignAdd]);

    const onDragOver = (event: React.DragEvent<HTMLLabelElement>) => {
        event.preventDefault();
        event.stopPropagation();
    };

    return (
        <Card>
            <div className="p-4">
                <h3 className="font-semibold text-lg mb-4">Upload Your Design</h3>
                 <label 
                    onDrop={onDrop}
                    onDragOver={onDragOver}
                    htmlFor="file-upload" 
                    className="flex flex-col items-center justify-center w-full h-48 border-2 border-slate-600 border-dashed rounded-lg cursor-pointer bg-slate-700/50 hover:bg-slate-700 transition-colors"
                >
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <UploadIcon className="w-10 h-10 mb-3 text-slate-400" />
                        <p className="mb-2 text-sm text-slate-400"><span className="font-semibold">Click to upload</span> or drag and drop</p>
                        <p className="text-xs text-slate-500">PNG, JPG, or WEBP (Transparent BG recommended)</p>
                    </div>
                    <input id="file-upload" type="file" className="hidden" accept="image/*" onChange={handleFileChange} />
                </label>
                {error && <p className="text-red-400 text-sm mt-2">{error}</p>}
            </div>
        </Card>
    );
};

export default ImageUploader;
