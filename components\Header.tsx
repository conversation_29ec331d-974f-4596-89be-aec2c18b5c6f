
import React from 'react';
import { Shirt } from 'lucide-react'; // Assuming lucide-react is available or use SVG
import Button from './ui/Button';

// A simple Shirt icon component if lucide-react isn't available
const ShirtIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M20.38 3.46 16 2a4 4 0 0 0-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z"></path>
  </svg>
);


interface HeaderProps {
    onBookNow: () => void;
}

const Header: React.FC<HeaderProps> = ({ onBookNow }) => {
  return (
    <header className="flex items-center justify-between p-4 bg-slate-800/50 backdrop-blur-sm border-b border-slate-700 shadow-md sticky top-0 z-50">
      <div className="flex items-center gap-3">
        <ShirtIcon />
        <h1 className="text-xl font-bold text-white">AI Print Designer</h1>
      </div>
      <Button onClick={onBookNow} variant="primary">
        Book Now
      </Button>
    </header>
  );
};

export default Header;
