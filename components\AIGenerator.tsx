
import React, { useState } from 'react';
import Input from './ui/Input';
import Button from './ui/Button';
import { generateImage } from '../services/geminiService';
import Spinner from './ui/Spinner';
import Card from './ui/Card';

interface AIGeneratorProps {
  onDesignAdd: (src: string, alt: string) => void;
}

const AIGenerator: React.FC<AIGeneratorProps> = ({ onDesignAdd }) => {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt.');
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const imageUrl = await generateImage(prompt);
      if (imageUrl) {
        onDesignAdd(imageUrl, `AI generated image: ${prompt}`);
      } else {
        setError('Failed to generate image. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <div className="p-4">
        <h3 className="font-semibold text-lg mb-4">Generate with AI</h3>
        <p className="text-sm text-slate-400 mb-4">
          Describe the design you want to create. Be as specific as possible for the best results.
        </p>
        <div className="space-y-4">
          <Input
            type="text"
            placeholder="e.g., a geometric wolf howling at a moon"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            disabled={isLoading}
          />
          <Button onClick={handleGenerate} disabled={isLoading} className="w-full">
            {isLoading ? <Spinner /> : 'Generate Design'}
          </Button>
          {error && <p className="text-red-400 text-sm mt-2">{error}</p>}
        </div>
      </div>
    </Card>
  );
};

export default AIGenerator;
