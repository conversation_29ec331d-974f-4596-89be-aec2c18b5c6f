
import React from 'react';
import { Design, ToolType } from '../types';
import Tabs from './ui/Tabs';
import ImageUploader from './ImageUploader';
import AIGenerator from './AIGenerator';
import DesignControls from './DesignControls';
import { UploadIcon } from './icons/UploadIcon';
import { SparklesIcon } from './icons/SparklesIcon';
import { AdjustmentsIcon } from './icons/AdjustmentsIcon';

interface ControlPanelProps {
  activeTool: ToolType;
  setActiveTool: (tool: ToolType) => void;
  design: Design | null;
  onDesignChange: (design: Design | null) => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  activeTool,
  setActiveTool,
  design,
  onDesignChange,
}) => {
  const tabs = [
    { id: ToolType.UPLOAD, label: 'Upload', icon: <UploadIcon /> },
    { id: ToolType.AI, label: 'Generate AI', icon: <SparklesIcon /> },
    { id: ToolType.ADJUST, label: 'Adjust', icon: <AdjustmentsIcon />, disabled: !design },
  ];

  const handleNewDesign = (src: string, alt: string) => {
    const img = new Image();
    img.onload = () => {
        onDesignChange({
            src,
            alt,
            position: { x: 50, y: 75 }, // Centered in the print area
            size: 1.0,
            rotation: 0,
            originalWidth: img.width,
            originalHeight: img.height
        });
    };
    img.src = src;
  };

  return (
    <div className="p-4 flex flex-col h-full">
      <Tabs tabs={tabs} activeTab={activeTool} onTabChange={setActiveTool} />
      <div className="mt-4 flex-grow">
        {activeTool === ToolType.UPLOAD && (
          <ImageUploader onDesignAdd={handleNewDesign} />
        )}
        {activeTool === ToolType.AI && (
          <AIGenerator onDesignAdd={handleNewDesign} />
        )}
        {activeTool === ToolType.ADJUST && design && (
          <DesignControls design={design} onDesignChange={onDesignChange} />
        )}
      </div>
    </div>
  );
};

export default ControlPanel;
